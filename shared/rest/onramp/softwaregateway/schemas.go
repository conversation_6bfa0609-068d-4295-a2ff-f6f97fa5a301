package softwaregateway

import (
	"time"

	"github.com/google/uuid"
)

// Structure of SoftwareGateway
type SoftwareGateway struct {
	Id                    uuid.UUID  `json:"id" db:"id"`
	OrganizationId        uuid.UUID  `json:"organizationid" db:"organizationid"`
	MachineKey            string     `json:"machinekey" db:"machinekey"`
	APIKey                string     `json:"apikey" db:"apikey"`
	Token                 string     `json:"-" db:"token"`
	GatewayVersion        *string    `json:"gatewayversion,omitempty" db:"gatewayversion"`
	DateLastCheckedInUTC  time.Time  `json:"datelastcheckedinutc" db:"datelastcheckedin"`
	PushConfigOnNextCheck bool       `json:"pushconfigonnextcheck" db:"pushconfigonnextcheck"`
	IsEnabled             bool       `json:"isenabled" db:"isenabled"`
	Config                string     `json:"config" db:"config"`
	Name                  string     `json:"name" db:"name"`
	Description           string     `json:"description" db:"description"`
	CreatedAt             time.Time  `json:"createdat" db:"createdat"`
	UpdatedAt             time.Time  `json:"updatedat" db:"updatedat"`
	DeletedAt             *time.Time `json:"-" db:"deletedat"`
	IsDeleted             bool       `json:"-" db:"isdeleted"`
	TemplateId            *uuid.UUID `json:"templateid,omitempty" db:"templateid"`
}

// The request body for creating a new software gateway
type CreateAndUpdateSoftwareGatewayRequest struct {
	Name           string    `json:"name" validate:"required,min=1"`
	Description    string    `json:"description" validate:"required,min=1"`
	MachineKey     string    `json:"machinekey" validate:"required,min=1"`
	OrganizationId uuid.UUID `json:"-"`
	IsEnabled      bool      `json:"isenabled"`
}

// SoftwareGatewayResponse represents the API response model
type SoftwareGatewayResponse struct {
	Id                    uuid.UUID  `json:"id"`
	OrganizationId        uuid.UUID  `json:"organizationid"`
	MachineKey            string     `json:"machinekey"`
	APIKey                string     `json:"apikey"`
	Token                 string     `json:"token"`
	GatewayVersion        *string    `json:"gatewayversion,omitempty"`
	DateLastCheckedInUTC  time.Time  `json:"datelastcheckedinutc"`
	PushConfigOnNextCheck bool       `json:"pushconfigonnextcheck"`
	IsEnabled             bool       `json:"isenabled"`
	Config                string     `json:"config"`
	Name                  string     `json:"name"`
	Description           string     `json:"description"`
	CreatedAt             time.Time  `json:"createdat"`
	UpdatedAt             time.Time  `json:"updatedat"`
	TemplateId            *uuid.UUID `json:"templateid,omitempty"`
}

// Converts a SoftwareGateway model to a SoftwareGatewayResponse
func (s *SoftwareGateway) ToResponse() SoftwareGatewayResponse {
	return SoftwareGatewayResponse{
		Id:                    s.Id,
		OrganizationId:        s.OrganizationId,
		MachineKey:            s.MachineKey,
		APIKey:                s.APIKey,
		Token:                 s.Token,
		GatewayVersion:        s.GatewayVersion,
		DateLastCheckedInUTC:  s.DateLastCheckedInUTC,
		PushConfigOnNextCheck: s.PushConfigOnNextCheck,
		IsEnabled:             s.IsEnabled,
		Config:                s.Config,
		Name:                  s.Name,
		Description:           s.Description,
		CreatedAt:             s.CreatedAt,
		UpdatedAt:             s.UpdatedAt,
		TemplateId:            s.TemplateId,
	}
}
