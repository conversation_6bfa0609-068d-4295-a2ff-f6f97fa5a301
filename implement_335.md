# Implementation Plan: OIDC Authentication Flow for Broker Service

## Requirement Summary
**Title:** As an engineer, I need to create the REST API to add multiple email addresses in the same account.

**Description:** 
- Onramp service supports both OIDC login (synapse OIDC button) and username/password login, returning session tokens
- Broker service currently only supports username/password login via `/user/authenticate` endpoint, returning JWT tokens
- **Goal:** Implement OIDC authentication flow in broker service that returns JWT tokens (similar to onramp's OIDC flow but with JWT output instead of session tokens)

## Current State Analysis

### Onramp Service (Reference Implementation)
- **OIDC Flow:** Uses `go-oidc` library with OAuth2 token exchange
- **Session Management:** Redis-based session store with session cookies
- **User Creation:** Automatic user creation for new OIDC users in `AuthMethod` table with `Type='OIDC'`
- **Token Output:** Session tokens stored in Redis

### Broker Service (Current State)
- **Authentication:** Username/password only via `/api/v3/user/authenticate`
- **JWT Handling:** Uses `shared/api/jwttokens` for JWT creation and validation
- **Token Storage:** JWT tokens stored in `UserToken` table
- **Authorization:** JWT-based middleware protection for protected endpoints

## Implementation Strategy

### Phase 1: Core OIDC Infrastructure Setup
Create the foundational OIDC components in broker service, mirroring onramp's architecture but adapted for JWT output.

### Phase 2: OIDC Authentication Handler
Implement the OIDC authentication endpoint that processes OAuth2 callbacks and returns JWTs.

### Phase 3: User Management Integration
Integrate OIDC user creation/lookup with existing broker user management and JWT token generation.

### Phase 4: Route Integration and Testing
Wire up the new OIDC endpoint and ensure proper integration with existing broker authentication middleware.

---

## Detailed Implementation Steps

## Phase 1: Core OIDC Infrastructure Setup

### Step 1.1: Create OIDC Configuration Module
**File:** `/workspace/microservices/broker/api/handlers/v3/user/oidc/config.go`

```go
package oidc

import (
    "context"
    "net"
    "net/http"
    "os"
    "strings"
    "time"

    "github.com/coreos/go-oidc"
    "golang.org/x/oauth2"
    "synapse-its.com/shared/logger"
)

// OIDCConfig holds the configuration for OpenID Connect (OIDC) authentication.
type OIDCConfig struct {
    ClientID     string
    ClientSecret string
    RedirectURL  string
    IssuerURL    string
    Provider     *oidc.Provider
    OAuth2Config *oauth2.Config
    Verifier     *oidc.IDTokenVerifier
    Scope        string
}

var (
    // SynapseOIDC is the global OIDC configuration for production
    SynapseOIDC = OIDCConfig{
        ClientID:     os.Getenv("SYNAPSE_OIDC_CLIENT_ID"),
        ClientSecret: os.Getenv("SYNAPSE_OIDC_CLIENT_SECRET"),
        RedirectURL:  os.Getenv("SYNAPSE_OIDC_BROKER_CALLBACK_URL"),
        IssuerURL:    os.Getenv("SYNAPSE_OIDC_ISSUER_URL"),
    }

    // SynapseOIDCLocal is the local development configuration
    SynapseOIDCLocal OIDCConfig

    SynapseOIDCScopes = []string{oidc.ScopeOpenID, "profile", "email"}

    // Custom HTTP client with timeout for OIDC operations
    oidcHTTPClient = &http.Client{
        Timeout: 30 * time.Second,
        Transport: &http.Transport{
            DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
                return (&net.Dialer{}).DialContext(ctx, network, addr)
            },
        },
    }
)

// InitializeOIDCConfig initializes the OIDC configuration
func InitializeOIDCConfig() error {
    ctx := context.Background()

    // Initialize production OIDC configuration
    var err error
    start := time.Now()
    for {
        SynapseOIDC.Provider, err = oidc.NewProvider(ctx, SynapseOIDC.IssuerURL)
        if err != nil {
            logger.Warnf("failed to init OIDC provider (retrying): %v", err)
            time.Sleep(time.Second)
            continue
        }
        break
    }
    logger.Infof("OIDC provider initialized in %v", time.Since(start))

    SynapseOIDC.Verifier = SynapseOIDC.Provider.Verifier(&oidc.Config{
        ClientID: SynapseOIDC.ClientID,
    })
    SynapseOIDC.OAuth2Config = &oauth2.Config{
        ClientID:     SynapseOIDC.ClientID,
        ClientSecret: SynapseOIDC.ClientSecret,
        Endpoint:     SynapseOIDC.Provider.Endpoint(),
        RedirectURL:  SynapseOIDC.RedirectURL,
        Scopes:       SynapseOIDCScopes,
    }

    // Initialize local development configuration
    SynapseOIDCLocal = SynapseOIDC
    SynapseOIDCLocal.IssuerURL = strings.ReplaceAll(
        SynapseOIDC.IssuerURL,
        "keycloak:8080",
        "localhost:8091",
    )
    SynapseOIDCLocal.RedirectURL = strings.ReplaceAll(
        SynapseOIDC.RedirectURL,
        "broker:8080",
        "localhost:8080",
    )

    return nil
}

// GetConfig returns the appropriate OIDC configuration based on environment
func GetConfig(isDev bool) *OIDCConfig {
    if isDev {
        return &SynapseOIDCLocal
    }
    return &SynapseOIDC
}
```

### Step 1.2: Create OAuth2 Token Exchanger Interface
**File:** `/workspace/microservices/broker/api/handlers/v3/user/oidc/exchanger.go`

```go
package oidc

import (
    "context"

    "github.com/coreos/go-oidc"
    "golang.org/x/oauth2"
)

// OAuth2TokenExchanger defines the interface for OAuth2 token exchange operations
type OAuth2TokenExchanger interface {
    Exchange(ctx context.Context, config *OIDCConfig, code string) (*oauth2.Token, error)
    VerifyIDToken(ctx context.Context, config *OIDCConfig, rawID string) (*oidc.IDToken, error)
    ExtractClaims(idToken *oidc.IDToken) (map[string]any, error)
}

// OAuth2TokenExchangerImpl implements OAuth2TokenExchanger
type OAuth2TokenExchangerImpl struct{}

// NewOAuth2TokenExchanger creates a new OAuth2 token exchanger
func NewOAuth2TokenExchanger() OAuth2TokenExchanger {
    return &OAuth2TokenExchangerImpl{}
}

// Exchange exchanges the authorization code for a token
func (e *OAuth2TokenExchangerImpl) Exchange(ctx context.Context, config *OIDCConfig, code string) (*oauth2.Token, error) {
    return config.OAuth2Config.Exchange(ctx, code)
}

// VerifyIDToken verifies and parses the ID token
func (e *OAuth2TokenExchangerImpl) VerifyIDToken(ctx context.Context, config *OIDCConfig, rawID string) (*oidc.IDToken, error) {
    return config.Verifier.Verify(ctx, rawID)
}

// ExtractClaims extracts claims from the ID token
func (e *OAuth2TokenExchangerImpl) ExtractClaims(idToken *oidc.IDToken) (map[string]any, error) {
    var claims map[string]any
    if err := idToken.Claims(&claims); err != nil {
        return nil, err
    }
    return claims, nil
}
```

### Step 1.3: Create OIDC Error Definitions
**File:** `/workspace/microservices/broker/api/handlers/v3/user/oidc/errors.go`

```go
package oidc

import "errors"

var (
    ErrExchangeFailed   = errors.New("failed to exchange authorization code for token")
    ErrMissingIDToken   = errors.New("id_token not found in OAuth2 token")
    ErrInvalidIDToken   = errors.New("invalid ID token")
    ErrInvalidState     = errors.New("invalid OAuth2 state parameter")
    ErrMissingClaims    = errors.New("required claims missing from ID token")
    ErrUserCreation     = errors.New("failed to create OIDC user")
    ErrJWTGeneration    = errors.New("failed to generate JWT token")
    ErrTokenPersistence = errors.New("failed to persist JWT token")
)
```

## Phase 2: OIDC Authentication Handler

### Step 2.1: Create OIDC Data Transfer Objects
**File:** `/workspace/microservices/broker/api/handlers/v3/user/oidc/schemas.go`

```go
package oidc

import "golang.org/x/oauth2"

// OIDCCallbackRequest represents the OAuth2 callback request
type OIDCCallbackRequest struct {
    Code        string         `json:"code"`
    State       string         `json:"state"`
    StateCookie string         `json:"state_cookie"`
    Claims      map[string]any `json:"claims"`
    OAuthToken  *oauth2.Token  `json:"oauth_token"`
}

// OIDCLoginRequest represents an OIDC login request
type OIDCLoginRequest struct {
    Subject string `json:"sub"`
    Issuer  string `json:"iss"`
    Email   string `json:"email"`
    Name    string `json:"name"`
}

// OIDCAuthResponse represents the response from OIDC authentication
type OIDCAuthResponse struct {
    User  userDetailRecord `json:"user"`
    Token string           `json:"token"`
}

// userDetailRecord represents user details in the response (reusing from authenticate package)
type userDetailRecord struct {
    UserID             int64  `json:"user_id"`
    UserIdentifier     string `json:"user_identifier"`
    OrganizationID     string `json:"organization_id"`
    Username           string `json:"username"`
    APIKey             string `json:"api_key"`
    Role               string `json:"role"`
}
```

### Step 2.2: Create OIDC Service Layer
**File:** `/workspace/microservices/broker/api/handlers/v3/user/oidc/service.go`

```go
package oidc

import (
    "context"
    "fmt"
    "strings"
    "time"

    "synapse-its.com/shared/api/jwttokens"
    "synapse-its.com/shared/api/security"
    "synapse-its.com/shared/connect"
    "synapse-its.com/shared/logger"
)

// OIDCService handles OIDC authentication business logic
type OIDCService struct {
    DBProvider             func(context.Context) (*connect.Connections, error)
    JwtCreator             func(string, time.Duration, jwttokens.UserPermissions) (string, time.Time, error)
    TokenPersister         func(connect.DatabaseExecutor, string, string, time.Time) error
    UserPermissionsCreator func(connect.DatabaseExecutor, string) (*jwttokens.UserPermissions, error)
}

// NewOIDCService creates a new OIDC service with default dependencies
func NewOIDCService() *OIDCService {
    return &OIDCService{
        DBProvider:             connect.GetConnections,
        JwtCreator:             jwttokens.CreateJwtTokenUsingDuration,
        TokenPersister:         persistTokenToDB,
        UserPermissionsCreator: createUserPermissions,
    }
}

// ProcessOIDCLogin handles OIDC authentication and returns JWT token
func (s *OIDCService) ProcessOIDCLogin(ctx context.Context, req *OIDCLoginRequest) (*OIDCAuthResponse, error) {
    // Validate input
    if req.Subject == "" || req.Issuer == "" {
        return nil, fmt.Errorf("missing required OIDC claims: subject or issuer")
    }

    // Get database connection
    connections, err := s.DBProvider(ctx)
    if err != nil {
        return nil, fmt.Errorf("database connection failed: %w", err)
    }
    pg := connections.Postgres

    // Check if user exists by OIDC subject and issuer
    user, err := s.getOrCreateOIDCUser(pg, req)
    if err != nil {
        return nil, fmt.Errorf("user lookup/creation failed: %w", err)
    }

    // Get user permissions
    userPermissions, err := s.UserPermissionsCreator(pg, user.Id)
    if err != nil {
        return nil, fmt.Errorf("failed to get user permissions: %w", err)
    }

    // Create JWT token (744 hours = ~31 days, matching existing broker behavior)
    duration := time.Duration(744) * time.Hour
    jwt, expiresAt, err := s.JwtCreator(user.Email, duration, *userPermissions)
    if err != nil {
        return nil, fmt.Errorf("JWT creation failed: %w", err)
    }

    // Persist token to database
    err = s.TokenPersister(pg, user.Id, jwt, expiresAt)
    if err != nil {
        return nil, fmt.Errorf("token persistence failed: %w", err)
    }

    // Create response
    userDetail := &userDetailRecord{
        UserID:             user.OrigID,
        UserIdentifier:     user.Id,
        OrganizationID:     user.OrganizationIdentifier,
        Username:           user.Email,
        APIKey:             user.Id, // Using user ID as API key (matching existing pattern)
        Role:               "user",  // Default role for OIDC users
    }

    return &OIDCAuthResponse{
        User:  *userDetail,
        Token: jwt,
    }, nil
}

// dbOIDCUser represents an OIDC user record from the database
type dbOIDCUser struct {
    Id                     string `db:"id"`
    OrigID                 int64  `db:"origid"`
    Email                  string `db:"email"`
    OrganizationIdentifier string `db:"organizationidentifier"`
}

// getOrCreateOIDCUser retrieves existing OIDC user or creates a new one
func (s *OIDCService) getOrCreateOIDCUser(pg connect.DatabaseExecutor, req *OIDCLoginRequest) (*dbOIDCUser, error) {
    // First, try to find existing user by OIDC subject and issuer
    query := `
        SELECT
            u.Id,
            u.OrigId,
            am.Email,
            COALESCE(m.OrganizationId::text, '') as OrganizationIdentifier
        FROM {{User}} u
        JOIN {{AuthMethod}} am ON am.UserId = u.Id
        LEFT JOIN {{Memberships}} m ON m.AuthMethodId = am.Id
        WHERE am.Type = 'OIDC'
            AND am.Sub = $1
            AND am.Issuer = $2
            AND am.IsEnabled = true
            AND NOT u.IsDeleted
        LIMIT 1`

    user := &dbOIDCUser{}
    err := pg.QueryRowStruct(user, query, req.Subject, req.Issuer)
    if err == nil {
        // User exists, update last login
        updateQuery := `
            UPDATE {{AuthMethod}}
            SET LastLogin = NOW()
            WHERE Sub = $1 AND Issuer = $2 AND Type = 'OIDC'`
        _, updateErr := pg.Exec(updateQuery, req.Subject, req.Issuer)
        if updateErr != nil {
            logger.Warnf("Failed to update last login for OIDC user: %v", updateErr)
        }
        return user, nil
    }

    // User doesn't exist, create new user
    return s.createOIDCUser(pg, req)
}

// createOIDCUser creates a new OIDC user and auth method
func (s *OIDCService) createOIDCUser(pg connect.DatabaseExecutor, req *OIDCLoginRequest) (*dbOIDCUser, error) {
    // Parse name into first/last name
    firstName := ""
    lastName := ""
    if req.Name != "" {
        nameParts := strings.Fields(req.Name)
        if len(nameParts) > 0 {
            firstName = nameParts[0]
        }
        if len(nameParts) > 1 {
            lastName = strings.Join(nameParts[1:], " ")
        }
    }

    // Create user record
    userQuery := `
        INSERT INTO {{User}} (
            FirstName,
            LastName,
            LastLogin
        ) VALUES (
            $1, $2, NOW()
        )
        RETURNING Id, OrigId`

    userRow, err := pg.QueryRow(userQuery, firstName, lastName)
    if err != nil {
        return nil, fmt.Errorf("failed to create user: %w", err)
    }

    userID, ok := userRow["id"].(string)
    if !ok {
        return nil, fmt.Errorf("user ID not returned from user creation")
    }

    origID, ok := userRow["origid"].(int64)
    if !ok {
        return nil, fmt.Errorf("user OrigID not returned from user creation")
    }

    // Create auth method record
    authMethodQuery := `
        INSERT INTO {{AuthMethod}} (
            UserId,
            Type,
            Sub,
            Issuer,
            Email,
            LastLogin
        ) VALUES (
            $1, 'OIDC', $2, $3, $4, NOW()
        )`

    _, err = pg.Exec(authMethodQuery, userID, req.Subject, req.Issuer, req.Email)
    if err != nil {
        return nil, fmt.Errorf("failed to create auth method: %w", err)
    }

    return &dbOIDCUser{
        Id:                     userID,
        OrigID:                 origID,
        Email:                  req.Email,
        OrganizationIdentifier: "", // New users don't have organization initially
    }, nil
}

// Helper functions (reused from authenticate package)
func persistTokenToDB(pg connect.DatabaseExecutor, userID string, jwt string, expiresAt time.Time) error {
    query := `
        INSERT INTO {{UserToken}} (UserId, JWTToken, JWTTokenSha256, Created, Expiration)
        VALUES ($1, $2, $3, $4, $5)`
    _, err := pg.Exec(query, userID, jwt, security.CalculateSHA256(jwt), time.Now().UTC(), expiresAt)
    if err != nil {
        logger.Warnf("error inserting user token into database: (%v)", err)
        return err
    }
    return nil
}

func createUserPermissions(pg connect.DatabaseExecutor, userID string) (*jwttokens.UserPermissions, error) {
    // Get user's software gateway permissions
    gatewayQuery := `
        SELECT usg.SoftwareGatewayId::text as machinekey
        FROM {{UserSoftwareGateway}} usg
        WHERE usg.UserId = $1 AND NOT usg.IsDeleted`

    gatewayRows, err := pg.Query(gatewayQuery, userID)
    if err != nil {
        return nil, err
    }
    defer gatewayRows.Close()

    var gatewayAccess []jwttokens.UserSoftwareGatewayAccess
    for gatewayRows.Next() {
        var access jwttokens.UserSoftwareGatewayAccess
        if err := gatewayRows.Scan(&access.SoftwareGateway); err != nil {
            return nil, err
        }
        gatewayAccess = append(gatewayAccess, access)
    }

    // Get user's device permissions (through software gateways)
    deviceQuery := `
        SELECT d.Id::text as device_id
        FROM {{Device}} d
        JOIN {{UserSoftwareGateway}} usg ON d.SoftwareGatewayId = usg.SoftwareGatewayId
        WHERE usg.UserId = $1 AND NOT usg.IsDeleted AND NOT d.IsDeleted`

    deviceRows, err := pg.Query(deviceQuery, userID)
    if err != nil {
        return nil, err
    }
    defer deviceRows.Close()

    var deviceAccess []jwttokens.UserDeviceAccess
    for deviceRows.Next() {
        var access jwttokens.UserDeviceAccess
        if err := deviceRows.Scan(&access.Device); err != nil {
            return nil, err
        }
        deviceAccess = append(deviceAccess, access)
    }

    return &jwttokens.UserPermissions{
        SoftwareGateway: gatewayAccess,
        Device:          deviceAccess,
    }, nil
}
```

### Step 2.3: Create OIDC Handler
**File:** `/workspace/microservices/broker/api/handlers/v3/user/oidc/handler.go`

```go
package oidc

import (
    "context"
    "net/http"
    "strings"

    "synapse-its.com/shared/api/response"
    "synapse-its.com/shared/logger"
    "synapse-its.com/shared/util"
)

// Handler handles OIDC authentication requests
type Handler struct {
    tokenExchanger OAuth2TokenExchanger
    oidcService    *OIDCService
}

// NewHandler creates a new OIDC handler
func NewHandler() *Handler {
    return &Handler{
        tokenExchanger: NewOAuth2TokenExchanger(),
        oidcService:    NewOIDCService(),
    }
}

// HandleOIDCCallback processes OAuth2 callback and returns JWT token
func (h *Handler) HandleOIDCCallback(w http.ResponseWriter, r *http.Request) {
    ctx := r.Context()

    // Determine if this is a development environment
    isDev := strings.Contains(r.Host, "localhost") || strings.Contains(r.Host, "127.0.0.1")

    // Process OAuth2 callback
    callbackResp, err := h.processOAuth2Callback(ctx, r, isDev)
    if err != nil {
        logger.Errorf("OIDC callback processing failed: %v", err)
        response.CreateUnauthorizedResponse(w)
        return
    }

    // Return JWT token response
    response.CreateAuthSuccessResponse(callbackResp, w)
}

// HandleOIDCLogin initiates OIDC login flow
func (h *Handler) HandleOIDCLogin(w http.ResponseWriter, r *http.Request) {
    isDev := strings.Contains(r.Host, "localhost") || strings.Contains(r.Host, "127.0.0.1")
    state := util.RandomString(32)

    // Set the state cookie for CSRF protection
    http.SetCookie(w, &http.Cookie{
        Name:     "oauth_state",
        Value:    state,
        Path:     "/",
        HttpOnly: true,
        Secure:   !isDev,
        SameSite: http.SameSiteLaxMode,
        MaxAge:   600, // 10 minutes
    })

    // Get OIDC configuration
    oidcConfig := GetConfig(isDev)

    // Redirect to OIDC provider
    authURL := oidcConfig.OAuth2Config.AuthCodeURL(state)
    http.Redirect(w, r, authURL, http.StatusFound)
}

// processOAuth2Callback handles the OAuth2 token exchange and user authentication
func (h *Handler) processOAuth2Callback(ctx context.Context, r *http.Request, isDev bool) (*OIDCAuthResponse, error) {
    // Get OIDC configuration
    oidcConfig := GetConfig(isDev)

    // Validate state parameter
    state := r.URL.Query().Get("state")
    stateCookie, err := r.Cookie("oauth_state")
    if err != nil || stateCookie.Value != state {
        return nil, ErrInvalidState
    }

    // Exchange authorization code for token
    code := r.URL.Query().Get("code")
    token, err := h.tokenExchanger.Exchange(ctx, oidcConfig, code)
    if err != nil {
        return nil, ErrExchangeFailed
    }

    // Extract and verify ID token
    rawIDToken, ok := token.Extra("id_token").(string)
    if !ok {
        return nil, ErrMissingIDToken
    }

    idToken, err := h.tokenExchanger.VerifyIDToken(ctx, oidcConfig, rawIDToken)
    if err != nil {
        return nil, ErrInvalidIDToken
    }

    // Extract claims from ID token
    claims, err := h.tokenExchanger.ExtractClaims(idToken)
    if err != nil {
        return nil, ErrInvalidIDToken
    }

    // Extract required claims
    sub, ok := claims["sub"].(string)
    if !ok {
        return nil, ErrMissingClaims
    }
    iss, ok := claims["iss"].(string)
    if !ok {
        return nil, ErrMissingClaims
    }

    email, _ := claims["email"].(string)
    name, _ := claims["name"].(string)

    // Create OIDC login request
    oidcLoginReq := &OIDCLoginRequest{
        Subject: sub,
        Issuer:  iss,
        Email:   email,
        Name:    name,
    }

    // Process OIDC login through service layer
    return h.oidcService.ProcessOIDCLogin(ctx, oidcLoginReq)
}
```

## Phase 3: Route Integration

### Step 3.1: Update Broker Routes
**File:** `/workspace/microservices/broker/api/routes/routes.go`

Add the following imports and route definitions:

```go
// Add to imports
V3UserOIDC "synapse-its.com/broker/api/handlers/v3/user/oidc"

// Add to NewRouter function after existing user routes
router.HandleFunc("/api/v3/user/oidc/login", V3UserOIDC.NewHandler().HandleOIDCLogin).Methods(http.MethodGet)
router.HandleFunc("/api/v3/user/oidc/callback", V3UserOIDC.NewHandler().HandleOIDCCallback).Methods(http.MethodGet)

// Legacy route support (if needed)
router.HandleFunc("/user/v3/oidc/login", V3UserOIDC.NewHandler().HandleOIDCLogin).Methods(http.MethodGet)
router.HandleFunc("/user/v3/oidc/callback", V3UserOIDC.NewHandler().HandleOIDCCallback).Methods(http.MethodGet)
```

### Step 3.2: Initialize OIDC Configuration in Main
**File:** `/workspace/microservices/broker/main.go`

Add OIDC initialization:

```go
// Add import
oidcConfig "synapse-its.com/broker/api/handlers/v3/user/oidc"

// Add to main() function before starting server
if err := oidcConfig.InitializeOIDCConfig(); err != nil {
    logger.Fatalf("Failed to initialize OIDC configuration: %v", err)
}
```

## Phase 4: Environment Configuration

### Step 4.1: Update Environment Variables
Add the following environment variables to your deployment configuration:

```bash
# OIDC Configuration for Broker Service
SYNAPSE_OIDC_BROKER_CALLBACK_URL=http://localhost:8080/api/v3/user/oidc/callback
# Note: Other OIDC variables (CLIENT_ID, CLIENT_SECRET, ISSUER_URL) should already exist from onramp
```

### Step 4.2: Update Docker Compose (if needed)
**File:** `/workspace/infra/docker-compose.dev.yml`

Ensure broker service has access to OIDC environment variables:

```yaml
broker:
  environment:
    - SYNAPSE_OIDC_BROKER_CALLBACK_URL=http://broker:8080/api/v3/user/oidc/callback
```

## Phase 5: Testing Strategy

### Step 5.1: Unit Tests
Create comprehensive unit tests for each component:

1. **OIDC Service Tests:** Test user creation, lookup, and JWT generation
2. **Handler Tests:** Test OAuth2 callback processing and error handling
3. **Token Exchanger Tests:** Mock OIDC provider interactions
4. **Integration Tests:** End-to-end OIDC flow testing

### Step 5.2: Manual Testing Flow
1. Navigate to `/api/v3/user/oidc/login`
2. Complete OIDC authentication with provider
3. Verify JWT token is returned in callback response
4. Test JWT token with protected broker endpoints
5. Verify user is created in database with OIDC auth method

## Implementation Notes

### Security Considerations
- State parameter validation prevents CSRF attacks
- Secure cookie settings for production environments
- JWT token expiration matches existing broker behavior
- OIDC token verification ensures authenticity

### Database Compatibility
- Uses existing `User` and `AuthMethod` tables
- OIDC users have `Type='OIDC'` in `AuthMethod` table
- Maintains compatibility with existing username/password authentication
- JWT tokens stored in existing `UserToken` table

### Error Handling
- Comprehensive error definitions for OIDC-specific failures
- Proper HTTP status codes for different error scenarios
- Detailed logging for debugging and monitoring
- Graceful fallback for configuration issues

### Scalability
- Stateless JWT-based authentication
- No session storage required (unlike onramp)
- Compatible with existing broker middleware
- Supports multiple OIDC providers through configuration
```
