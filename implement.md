# OIDC Authentication Implementation for Broker Microservice

## Requirement Analysis
**Title:** As a backend, I need to be able to authenticate a user using OIDC in broker

## Overview
This document provides a detailed step-by-step implementation plan to add OpenID Connect (OIDC) authentication capabilities to the broker microservice. The implementation will leverage existing patterns from the onramp microservice while adapting them to the broker's architecture and requirements.

## Current State Analysis

### Existing Authentication in Broker
- **Current Method**: Username/password authentication via `/api/v3/user/authenticate`
- **JWT Token System**: Uses RSA-signed JWT tokens with user permissions
- **Middleware**: `JWTAuthorizerMiddleware` protects specific endpoints
- **Token Storage**: Tokens persisted to database with expiration

### Existing OIDC Implementation (Onramp Reference)
- **OIDC Provider**: Keycloak integration with `github.com/coreos/go-oidc`
- **OAuth2 Flow**: Authorization code flow with state validation
- **Session Management**: Redis-based session storage
- **Token Exchange**: ID token verification and claims extraction
- **User Creation**: Automatic user provisioning from OIDC claims

## Implementation Plan

### Phase 1: Dependencies and Configuration

#### Step 1.1: Add OIDC Dependencies to Broker
**File**: `/workspace/microservices/broker/go.mod`

Add the following dependencies:
```go
github.com/coreos/go-oidc v2.3.0+incompatible
golang.org/x/oauth2 v0.29.0
```

**Action**: Use `go get` to add dependencies:
```bash
cd /workspace/microservices/broker
go get github.com/coreos/go-oidc@v2.3.0+incompatible
go get golang.org/x/oauth2@v0.29.0
```

#### Step 1.2: Environment Configuration
**Files**: 
- `/workspace/infra/docker-compose.dev.yml` (update broker service)
- Documentation for production deployment

Add environment variables for OIDC configuration:
```yaml
BROKER_OIDC_CLIENT_ID: "broker-client"
BROKER_OIDC_CLIENT_SECRET: "broker-client-secret"
BROKER_OIDC_ISSUER_URL: "http://keycloak:8080/realms/synapse"
BROKER_OIDC_REDIRECT_URL: "http://localhost:8080/api/v3/auth/oidc/callback"
```

### Phase 2: Core OIDC Infrastructure

#### Step 2.1: Create OIDC Configuration Module
**File**: `/workspace/microservices/broker/api/auth/oidc/config.go`

Create OIDC configuration structure similar to onramp:
```go
package oidc

import (
    "context"
    "net"
    "os"
    "time"
    
    "github.com/coreos/go-oidc"
    "golang.org/x/oauth2"
    "synapse-its.com/shared/logger"
)

type OIDCConfig struct {
    ClientID     string
    ClientSecret string
    RedirectURL  string
    IssuerURL    string
    Provider     *oidc.Provider
    OAuth2Config *oauth2.Config
    Verifier     *oidc.IDTokenVerifier
    Scope        string
}

var (
    BrokerOIDC = OIDCConfig{
        ClientID:     os.Getenv("BROKER_OIDC_CLIENT_ID"),
        ClientSecret: os.Getenv("BROKER_OIDC_CLIENT_SECRET"),
        RedirectURL:  os.Getenv("BROKER_OIDC_REDIRECT_URL"),
        IssuerURL:    os.Getenv("BROKER_OIDC_ISSUER_URL"),
    }
    
    BrokerOIDCScopes = []string{oidc.ScopeOpenID, "profile", "email"}
)
```

#### Step 2.2: Create Token Exchange Interface
**File**: `/workspace/microservices/broker/api/auth/oidc/exchanger.go`

Implement OAuth2 token exchange interface:
```go
package oidc

import (
    "context"
    
    "github.com/coreos/go-oidc"
    "golang.org/x/oauth2"
)

type OAuth2TokenExchanger interface {
    Exchange(ctx context.Context, config *OIDCConfig, code string) (*oauth2.Token, error)
    VerifyIDToken(ctx context.Context, config *OIDCConfig, rawID string) (*oidc.IDToken, error)
    ExtractClaims(idToken *oidc.IDToken) (map[string]any, error)
}

type OAuth2TokenExchangerImpl struct{}

func NewOAuth2TokenExchanger() OAuth2TokenExchanger {
    return &OAuth2TokenExchangerImpl{}
}
```

### Phase 3: OIDC Authentication Handlers

#### Step 3.1: Create OIDC Login Handler
**File**: `/workspace/microservices/broker/api/handlers/v3/auth/oidc/login/handler.go`

Following the domain-nested REST API structure:
```go
package login

import (
    "net/http"
    "strings"
    
    "synapse-its.com/broker/api/auth/oidc"
    "synapse-its.com/shared/api/response"
    "synapse-its.com/shared/util"
)

func Handler(w http.ResponseWriter, r *http.Request) {
    state := util.RandomString(32)
    
    // Set state cookie for CSRF protection
    http.SetCookie(w, &http.Cookie{
        Name:     "oauth_state",
        Value:    state,
        Path:     "/",
        HttpOnly: true,
        Secure:   !strings.Contains(r.Host, "localhost"),
        SameSite: http.SameSiteLaxMode,
        MaxAge:   300, // 5 minutes
    })
    
    // Redirect to OIDC provider
    authURL := oidc.BrokerOIDC.OAuth2Config.AuthCodeURL(state)
    http.Redirect(w, r, authURL, http.StatusFound)
}
```

#### Step 3.2: Create OIDC Callback Handler
**File**: `/workspace/microservices/broker/api/handlers/v3/auth/oidc/callback/handler.go`

Handle OAuth2 callback and token exchange:
```go
package callback

import (
    "context"
    "net/http"
    
    "synapse-its.com/broker/api/auth/oidc"
    "synapse-its.com/shared/api/response"
    "synapse-its.com/shared/connect"
    "synapse-its.com/shared/logger"
)

type CallbackHandler struct {
    tokenExchanger oidc.OAuth2TokenExchanger
    userService    UserService
}

func Handler(w http.ResponseWriter, r *http.Request) {
    handler := &CallbackHandler{
        tokenExchanger: oidc.NewOAuth2TokenExchanger(),
        userService:    NewUserService(),
    }
    
    handler.handleCallback(w, r)
}
```

### Phase 4: User Management Integration

#### Step 4.1: Create OIDC User Service
**File**: `/workspace/microservices/broker/api/handlers/v3/auth/oidc/callback/user_service.go`

Handle user creation and authentication:
```go
package callback

import (
    "context"
    "time"
    
    "synapse-its.com/shared/api/jwttokens"
    "synapse-its.com/shared/connect"
)

type UserService interface {
    AuthenticateOIDCUser(ctx context.Context, claims map[string]any) (*AuthResult, error)
}

type AuthResult struct {
    User   *User
    Token  string
    Claims map[string]any
}

type userService struct{}

func NewUserService() UserService {
    return &userService{}
}
```

#### Step 4.2: Database Schema Updates
**File**: `/workspace/schemas/data-core-pg/migrations/add_oidc_auth_method.sql`

Add OIDC support to existing AuthMethod table:
```sql
-- Add OIDC fields to AuthMethod table if not exists
ALTER TABLE "AuthMethod" 
ADD COLUMN IF NOT EXISTS "OIDCSubject" VARCHAR(255),
ADD COLUMN IF NOT EXISTS "OIDCIssuer" VARCHAR(255);

-- Create index for OIDC lookups
CREATE INDEX IF NOT EXISTS "idx_authmethod_oidc" 
ON "AuthMethod" ("OIDCSubject", "OIDCIssuer") 
WHERE "OIDCSubject" IS NOT NULL;
```

### Phase 5: Route Integration

#### Step 5.1: Update Router Configuration
**File**: `/workspace/microservices/broker/api/routes/routes.go`

Add OIDC routes to the existing router:
```go
// Add imports
V3AuthOIDCLogin "synapse-its.com/broker/api/handlers/v3/auth/oidc/login"
V3AuthOIDCCallback "synapse-its.com/broker/api/handlers/v3/auth/oidc/callback"

// Add routes in NewRouter function
router.HandleFunc("/api/v3/auth/oidc/login", V3AuthOIDCLogin.Handler).Methods(http.MethodGet)
router.HandleFunc("/api/v3/auth/oidc/callback", V3AuthOIDCCallback.Handler).Methods(http.MethodGet)
```

#### Step 5.2: Initialize OIDC Configuration
**File**: `/workspace/microservices/broker/main.go`

Add OIDC initialization similar to onramp:
```go
// Add to main.go init() function or Run() function
func initOIDC() error {
    ctx := context.Background()
    
    var err error
    oidc.BrokerOIDC.Provider, err = oidc.NewProvider(ctx, oidc.BrokerOIDC.IssuerURL)
    if err != nil {
        return fmt.Errorf("failed to initialize OIDC provider: %w", err)
    }
    
    oidc.BrokerOIDC.Verifier = oidc.BrokerOIDC.Provider.Verifier(&oidc.Config{
        ClientID: oidc.BrokerOIDC.ClientID,
    })
    
    oidc.BrokerOIDC.OAuth2Config = &oauth2.Config{
        ClientID:     oidc.BrokerOIDC.ClientID,
        ClientSecret: oidc.BrokerOIDC.ClientSecret,
        Endpoint:     oidc.BrokerOIDC.Provider.Endpoint(),
        RedirectURL:  oidc.BrokerOIDC.RedirectURL,
        Scopes:       oidc.BrokerOIDCScopes,
    }
    
    return nil
}
```

### Phase 6: Testing and Validation

#### Step 6.1: Unit Tests
Create comprehensive unit tests for each component:
- `/workspace/microservices/broker/api/auth/oidc/config_test.go`
- `/workspace/microservices/broker/api/auth/oidc/exchanger_test.go`
- `/workspace/microservices/broker/api/handlers/v3/auth/oidc/login/handler_test.go`
- `/workspace/microservices/broker/api/handlers/v3/auth/oidc/callback/handler_test.go`

#### Step 6.2: Integration Tests
Create integration tests that verify:
- OIDC provider connectivity
- Token exchange flow
- User creation/authentication
- JWT token generation

### Phase 7: Documentation and Deployment

#### Step 7.1: API Documentation
Update Swagger/OpenAPI specification:
- Add OIDC endpoints to `/workspace/microservices/broker/openapi/broker.yaml`
- Include authentication flow documentation

#### Step 7.2: Environment Configuration
Document required environment variables and update deployment configurations.

## Implementation Order

1. **Dependencies** (Step 1.1-1.2): Add OIDC libraries and environment configuration
2. **Core Infrastructure** (Step 2.1-2.2): Create OIDC configuration and token exchange
3. **Database Schema** (Step 4.2): Update database to support OIDC users
4. **Authentication Handlers** (Step 3.1-3.2): Implement login and callback endpoints
5. **User Management** (Step 4.1): Create user service for OIDC authentication
6. **Route Integration** (Step 5.1-5.2): Wire up routes and initialize OIDC
7. **Testing** (Step 6.1-6.2): Comprehensive testing suite
8. **Documentation** (Step 7.1-7.2): Update documentation and deployment guides

## Security Considerations

1. **State Validation**: Implement CSRF protection using state parameter
2. **Token Validation**: Verify ID tokens using OIDC provider's public keys
3. **Secure Cookies**: Use HttpOnly, Secure, and SameSite cookie attributes
4. **Error Handling**: Avoid leaking sensitive information in error responses
5. **Rate Limiting**: Consider implementing rate limiting for authentication endpoints

## Compatibility Notes

1. **Existing Authentication**: Maintain backward compatibility with username/password authentication
2. **JWT Format**: Ensure OIDC-generated JWTs are compatible with existing authorization middleware
3. **User Permissions**: Map OIDC claims to existing user permission system
4. **Database Schema**: Extend existing tables rather than creating new ones where possible

This implementation plan provides a comprehensive roadmap for adding OIDC authentication to the broker microservice while maintaining consistency with existing patterns and ensuring security best practices.

## Detailed Implementation Steps

### Step-by-Step Implementation Guide

#### Phase 1 Implementation Details

**Step 1.1: Add Dependencies**
```bash
cd /workspace/microservices/broker
go get github.com/coreos/go-oidc@v2.3.0+incompatible
go get golang.org/x/oauth2@v0.29.0
go mod tidy
```

**Step 1.2: Update Docker Compose**
Add to broker service in `/workspace/infra/docker-compose.dev.yml`:
```yaml
environment:
  - BROKER_OIDC_CLIENT_ID=broker-client
  - BROKER_OIDC_CLIENT_SECRET=broker-client-secret
  - BROKER_OIDC_ISSUER_URL=http://keycloak:8080/realms/synapse
  - BROKER_OIDC_REDIRECT_URL=http://localhost:8080/api/v3/auth/oidc/callback
```

#### Phase 2 Implementation Details

**Step 2.1: Complete OIDC Config Implementation**
```go
// /workspace/microservices/broker/api/auth/oidc/config.go
package oidc

import (
    "context"
    "net"
    "net/http"
    "os"
    "time"

    "github.com/coreos/go-oidc"
    "golang.org/x/oauth2"
    "synapse-its.com/shared/logger"
)

// Custom HTTP client for development (similar to onramp)
var customHTTPClient = &http.Client{
    Timeout: 30 * time.Second,
    Transport: &http.Transport{
        DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
            if addr == "keycloak:8080" {
                addr = "localhost:8091"
            }
            return (&net.Dialer{}).DialContext(ctx, network, addr)
        },
    },
}

func InitializeOIDC() error {
    ctx := context.WithValue(context.Background(), oauth2.HTTPClient, customHTTPClient)

    var err error
    BrokerOIDC.Provider, err = oidc.NewProvider(ctx, BrokerOIDC.IssuerURL)
    if err != nil {
        logger.Warnf("Failed to initialize OIDC provider: %v", err)
        return err
    }

    BrokerOIDC.Verifier = BrokerOIDC.Provider.Verifier(&oidc.Config{
        ClientID: BrokerOIDC.ClientID,
    })

    BrokerOIDC.OAuth2Config = &oauth2.Config{
        ClientID:     BrokerOIDC.ClientID,
        ClientSecret: BrokerOIDC.ClientSecret,
        Endpoint:     BrokerOIDC.Provider.Endpoint(),
        RedirectURL:  BrokerOIDC.RedirectURL,
        Scopes:       BrokerOIDCScopes,
    }

    logger.Info("OIDC configuration initialized successfully")
    return nil
}
```

**Step 2.2: Complete Token Exchanger Implementation**
```go
// /workspace/microservices/broker/api/auth/oidc/exchanger.go
func (e *OAuth2TokenExchangerImpl) Exchange(ctx context.Context, config *OIDCConfig, code string) (*oauth2.Token, error) {
    return config.OAuth2Config.Exchange(ctx, code)
}

func (e *OAuth2TokenExchangerImpl) VerifyIDToken(ctx context.Context, config *OIDCConfig, rawID string) (*oidc.IDToken, error) {
    return config.Verifier.Verify(ctx, rawID)
}

func (e *OAuth2TokenExchangerImpl) ExtractClaims(idToken *oidc.IDToken) (map[string]any, error) {
    var claims map[string]any
    if err := idToken.Claims(&claims); err != nil {
        return nil, err
    }
    return claims, nil
}
```

#### Phase 3 Implementation Details

**Step 3.1: Complete Login Handler**
```go
// /workspace/microservices/broker/api/handlers/v3/auth/oidc/login/handler.go
package login

import (
    "net/http"
    "strings"

    "synapse-its.com/broker/api/auth/oidc"
    "synapse-its.com/shared/api/response"
    "synapse-its.com/shared/logger"
    "synapse-its.com/shared/util"
)

func Handler(w http.ResponseWriter, r *http.Request) {
    // Generate state for CSRF protection
    state := util.RandomString(32)

    // Set state cookie
    isDev := strings.Contains(r.Host, "localhost")
    http.SetCookie(w, &http.Cookie{
        Name:     "oauth_state",
        Value:    state,
        Path:     "/",
        HttpOnly: true,
        Secure:   !isDev,
        SameSite: http.SameSiteLaxMode,
        MaxAge:   300, // 5 minutes
    })

    // Check if OIDC is configured
    if oidc.BrokerOIDC.OAuth2Config == nil {
        logger.Error("OIDC not configured")
        response.CreateInternalErrorResponse(w)
        return
    }

    // Redirect to OIDC provider
    authURL := oidc.BrokerOIDC.OAuth2Config.AuthCodeURL(state)
    logger.Debugf("Redirecting to OIDC provider: %s", authURL)
    http.Redirect(w, r, authURL, http.StatusFound)
}
```

**Step 3.2: Complete Callback Handler Structure**
```go
// /workspace/microservices/broker/api/handlers/v3/auth/oidc/callback/handler.go
package callback

import (
    "context"
    "net/http"

    "synapse-its.com/broker/api/auth/oidc"
    "synapse-its.com/shared/api/response"
    "synapse-its.com/shared/connect"
    "synapse-its.com/shared/logger"
)

type CallbackHandler struct {
    tokenExchanger oidc.OAuth2TokenExchanger
    userService    UserService
}

func Handler(w http.ResponseWriter, r *http.Request) {
    handler := &CallbackHandler{
        tokenExchanger: oidc.NewOAuth2TokenExchanger(),
        userService:    NewUserService(),
    }

    handler.handleCallback(w, r)
}

func (h *CallbackHandler) handleCallback(w http.ResponseWriter, r *http.Request) {
    ctx := r.Context()

    // Validate state parameter
    if err := h.validateState(r); err != nil {
        logger.Infof("State validation failed: %v", err)
        response.CreateUnauthorizedResponse(w)
        return
    }

    // Exchange code for token
    code := r.URL.Query().Get("code")
    if code == "" {
        logger.Info("Missing authorization code")
        response.CreateUnauthorizedResponse(w)
        return
    }

    // Process OAuth2 callback
    result, err := h.processCallback(ctx, code)
    if err != nil {
        logger.Errorf("Callback processing failed: %v", err)
        response.CreateUnauthorizedResponse(w)
        return
    }

    // Return JWT token
    response.CreateAuthSuccessResponse(result, w)
}
```

#### Phase 4 Implementation Details

**Step 4.1: User Service Implementation**
```go
// /workspace/microservices/broker/api/handlers/v3/auth/oidc/callback/user_service.go
package callback

import (
    "context"
    "fmt"
    "time"

    "synapse-its.com/shared/api/jwttokens"
    "synapse-its.com/shared/connect"
    "synapse-its.com/shared/logger"
)

type User struct {
    ID       int64  `json:"id"`
    OrigID   string `json:"orig_id"`
    Username string `json:"username"`
    Email    string `json:"email"`
}

type AuthResult struct {
    User  *User  `json:"user"`
    Token string `json:"token"`
}

func (s *userService) AuthenticateOIDCUser(ctx context.Context, claims map[string]any) (*AuthResult, error) {
    // Extract required claims
    sub, ok := claims["sub"].(string)
    if !ok {
        return nil, fmt.Errorf("missing subject claim")
    }

    iss, ok := claims["iss"].(string)
    if !ok {
        return nil, fmt.Errorf("missing issuer claim")
    }

    email, _ := claims["email"].(string)
    name, _ := claims["name"].(string)

    // Get database connection
    connections, err := connect.GetConnections(ctx)
    if err != nil {
        return nil, fmt.Errorf("database connection failed: %w", err)
    }

    // Find or create user
    user, err := s.findOrCreateOIDCUser(connections.Postgres, sub, iss, email, name)
    if err != nil {
        return nil, fmt.Errorf("user management failed: %w", err)
    }

    // Generate JWT token
    duration := 744 * time.Hour // Match existing token duration
    userPermissions := jwttokens.UserPermissions{} // TODO: Map from user data

    token, _, err := jwttokens.CreateJwtTokenUsingDuration(user.Username, duration, userPermissions)
    if err != nil {
        return nil, fmt.Errorf("token creation failed: %w", err)
    }

    return &AuthResult{
        User:  user,
        Token: token,
    }, nil
}
```

#### Phase 5 Implementation Details

**Step 5.1: Database Integration**
```go
// /workspace/microservices/broker/api/handlers/v3/auth/oidc/callback/database.go
package callback

import (
    "database/sql"
    "fmt"

    "synapse-its.com/shared/connect"
    "synapse-its.com/shared/logger"
)

func (s *userService) findOrCreateOIDCUser(pg connect.DatabaseExecutor, subject, issuer, email, name string) (*User, error) {
    // First, try to find existing user by OIDC subject
    user, err := s.findUserByOIDCSubject(pg, subject, issuer)
    if err == nil {
        return user, nil
    }

    if err != sql.ErrNoRows {
        return nil, fmt.Errorf("database query failed: %w", err)
    }

    // User doesn't exist, create new one
    return s.createOIDCUser(pg, subject, issuer, email, name)
}

func (s *userService) findUserByOIDCSubject(pg connect.DatabaseExecutor, subject, issuer string) (*User, error) {
    query := `
        SELECT u.Id, u.OrigID, am.Username, am.Email
        FROM {{User}} u
        JOIN {{AuthMethod}} am ON am.UserId = u.Id
        WHERE am.OIDCSubject = $1 AND am.OIDCIssuer = $2 AND am.IsEnabled
        LIMIT 1
    `

    user := &User{}
    err := pg.QueryRowStruct(user, query, subject, issuer)
    return user, err
}

func (s *userService) createOIDCUser(pg connect.DatabaseExecutor, subject, issuer, email, name string) (*User, error) {
    // Implementation for creating new OIDC user
    // This would involve creating both User and AuthMethod records
    // Following the pattern from onramp's CreateOIDCUser

    // TODO: Implement user creation logic
    return nil, fmt.Errorf("user creation not yet implemented")
}
```

## Testing Strategy

### Unit Test Examples

**OIDC Config Test**:
```go
// /workspace/microservices/broker/api/auth/oidc/config_test.go
func TestInitializeOIDC(t *testing.T) {
    // Test OIDC initialization with mock provider
}
```

**Handler Tests**:
```go
// /workspace/microservices/broker/api/handlers/v3/auth/oidc/login/handler_test.go
func TestLoginHandler(t *testing.T) {
    // Test login redirect and state cookie setting
}

// /workspace/microservices/broker/api/handlers/v3/auth/oidc/callback/handler_test.go
func TestCallbackHandler(t *testing.T) {
    // Test callback processing with mock token exchange
}
```

## Deployment Checklist

1. **Environment Variables**: Ensure all OIDC environment variables are set
2. **Keycloak Configuration**: Create broker client in Keycloak
3. **Database Migration**: Run OIDC schema updates
4. **Testing**: Verify OIDC flow end-to-end
5. **Documentation**: Update API documentation
6. **Monitoring**: Add logging and metrics for OIDC endpoints

## Error Handling Patterns

Follow existing broker patterns:
- Use `response.CreateUnauthorizedResponse(w)` for auth failures
- Use `response.CreateInternalErrorResponse(w)` for system errors
- Log errors with appropriate levels (Info for user errors, Error for system errors)
- Don't expose sensitive information in error responses

## Security Best Practices

1. **State Parameter**: Always validate OAuth2 state parameter
2. **Token Validation**: Verify ID tokens using provider's public keys
3. **Secure Cookies**: Use HttpOnly, Secure, and SameSite attributes
4. **HTTPS**: Ensure HTTPS in production for redirect URLs
5. **Rate Limiting**: Consider implementing rate limiting for auth endpoints
6. **Audit Logging**: Log authentication events for security monitoring
