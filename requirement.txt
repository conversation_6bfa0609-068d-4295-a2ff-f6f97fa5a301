Title:
As an engineer, I need to create the REST API to add multiple email addresses in the same account.

Description:
With the onramp service, we allow OIDC login (synapse OIDC button) and username/password login (the username/password fields above the synapse OIDC button). So the website supports both flows.

Both of these flows return a session token which is used for future authorization.

 

On the other-hand, broker only supports the username/password login using the /user/authenticate endpoint.

This flow returns the JWT assigned to the user which is used for future authorization.

 

We will need to implement a OIDC authentication flow into the broker which returns a JWT assigned to the user for future authentication. We will need to be implementing the flow from onramp but returning the JWT.